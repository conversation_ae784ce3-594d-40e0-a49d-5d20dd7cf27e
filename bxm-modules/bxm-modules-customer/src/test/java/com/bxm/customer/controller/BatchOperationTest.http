### 批量操作增值交付单 - 批量确认
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_CONFIRM",
  "reason": "批量确认交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量确认操作"
}

### 批量操作增值交付单 - 批量提交
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_SUBMIT",
  "reason": "批量提交交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量提交操作"
}

### 批量操作增值交付单 - 批量关闭扣款
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_CLOSE_DEDUCTION",
  "reason": "批量关闭扣款",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量关闭扣款操作"
}

### 批量操作增值交付单 - 批量关闭交付
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_CLOSE_DELIVERY",
  "reason": "批量关闭交付",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量关闭交付操作"
}

### 批量操作增值交付单 - 批量解除扣款异常
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_RESOLVE_DEDUCTION_EXCEPTION",
  "reason": "批量解除扣款异常",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量解除扣款异常操作"
}

### 批量操作增值交付单 - 批量解除交付异常
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_RESOLVE_DELIVERY_EXCEPTION",
  "reason": "批量解除交付异常",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量解除交付异常操作"
}

### 批量操作增值交付单 - 批量驳回
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_REJECT",
  "reason": "批量驳回交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量驳回操作"
}

### 批量操作增值交付单 - 批量退回（已扣款 → 待扣款）
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_RETURN",
  "sourceStatus": "DEDUCTION_COMPLETED",
  "reason": "批量退回交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量退回操作：已扣款 → 待扣款"
}

### 批量操作增值交付单 - 批量退回（待扣款 → 待确认）
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_RETURN",
  "sourceStatus": "CONFIRMED_PENDING_DEDUCTION",
  "reason": "批量退回交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量退回操作：待扣款 → 待确认"
}

### 批量操作增值交付单 - 批量退回（待确认 → 待交付）
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_RETURN",
  "sourceStatus": "PENDING_CONFIRMATION",
  "reason": "批量退回交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量退回操作：待确认 → 待交付"
}

### 批量操作增值交付单 - 批量退回（待交付 → 待提交）
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_RETURN",
  "sourceStatus": "SUBMITTED_PENDING_DELIVERY",
  "reason": "批量退回交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量退回操作：待交付 → 待提交"
}

### 导出批量操作异常数据
GET {{host}}/customer/valueAdded/deliveryOrder/exportBatchErrors/{{batchNo}}

### 示例：导出批量操作异常数据（使用具体的批次号）
GET {{host}}/customer/valueAdded/deliveryOrder/exportBatchErrors/a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6

### ========================================
### 分派增值交付单测试
### ========================================

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/x-www-form-urlencoded
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6ImZiMDI2ZjkyLWU4YzQtNDFiZC1hMWU4LWM4ZjVmM2ZiNTBhNCIsInVzZXJuYW1lIjoiYWRtaW4ifQ.bKQWhFmWnUaNkYiJC2tkaXv1UHxGZDdF-cV5lflkTXVSwVWULa4YKW8rNVzL8rnNn320TCUDGbz47U33bG3Crw

### 1. 正常分派测试 - 分派2个交付单到会计部门
POST {{baseUrl}}/valuedAddedDeliveryOrder/dispatch
Content-Type: {{contentType}}
Authorization: {{authorization}}

deliveryOrders=VAD2508051430001A1C&deliveryOrders=VAD2508051430002A1C&accountingDeptId=1001

### 2. 正常分派测试 - 分派单个交付单
POST {{baseUrl}}/valuedAddedDeliveryOrder/dispatch
Content-Type: {{contentType}}
Authorization: {{authorization}}

deliveryOrders=VAD2508051430003A1C&accountingDeptId=1002

### 3. 批量分派测试 - 分派5个交付单
POST {{baseUrl}}/valuedAddedDeliveryOrder/dispatch
Content-Type: {{contentType}}
Authorization: {{authorization}}

deliveryOrders=*******************&deliveryOrders=*******************&accountingDeptId=1003

### 4. 参数验证测试 - 空的交付单列表
POST {{baseUrl}}/valuedAddedDeliveryOrder/dispatch
Content-Type: {{contentType}}
Authorization: {{authorization}}

accountingDeptId=1001

### 5. 参数验证测试 - 缺少会计部门ID
POST {{baseUrl}}/valuedAddedDeliveryOrder/dispatch
Content-Type: {{contentType}}
Authorization: {{authorization}}

deliveryOrders=VAD2508051430001A1C&deliveryOrders=VAD2508051430002A1C

### 6. 参数验证测试 - 会计部门ID为空
POST {{baseUrl}}/valuedAddedDeliveryOrder/dispatch
Content-Type: {{contentType}}
Authorization: {{authorization}}

deliveryOrders=VAD2508051430001A1C&accountingDeptId=

### 7. 业务验证测试 - 不存在的交付单编号
POST {{baseUrl}}/valuedAddedDeliveryOrder/dispatch
Content-Type: {{contentType}}
Authorization: {{authorization}}

deliveryOrders=INVALID_ORDER_001&deliveryOrders=INVALID_ORDER_002&accountingDeptId=1001

### 8. 混合测试 - 部分存在的交付单编号
POST {{baseUrl}}/valuedAddedDeliveryOrder/dispatch
Content-Type: {{contentType}}
Authorization: {{authorization}}

deliveryOrders=VAD2508051430001A1C&deliveryOrders=INVALID_ORDER_001&deliveryOrders=VAD2508051430002A1C&accountingDeptId=1001

### 9. 边界测试 - 重复的交付单编号（应该自动去重）
POST {{baseUrl}}/valuedAddedDeliveryOrder/dispatch
Content-Type: {{contentType}}
Authorization: {{authorization}}

deliveryOrders=VAD2508051430001A1C&deliveryOrders=VAD2508051430001A1C&deliveryOrders=VAD2508051430002A1C&accountingDeptId=1001

### 10. 边界测试 - 包含空值的交付单编号（应该自动过滤）
POST {{baseUrl}}/valuedAddedDeliveryOrder/dispatch
Content-Type: {{contentType}}
Authorization: {{authorization}}

deliveryOrders=VAD2508051430001A1C&deliveryOrders=&deliveryOrders=VAD2508051430002A1C&accountingDeptId=1001

### ========================================
### 测试说明：
### 1. 成功的分派请求会返回：{"code": 200, "msg": "分派完成，成功分派 X 条交付单", "data": "分派完成，成功分派 X 条交付单"}
### 2. 失败的分派请求会返回：{"code": 500, "msg": "错误信息", "data": null}
### 3. 参数验证失败会返回具体的验证错误信息
### 4. 业务验证失败会返回不存在的交付单编号列表
### 5. 请确保测试的交付单编号在数据库中存在
### 6. 请确保会计部门ID是有效的部门ID
### 7. 建议先运行单个交付单测试，确认基本功能正常
### 8. 系统会自动过滤空值和去重，确保数据质量
### ========================================
